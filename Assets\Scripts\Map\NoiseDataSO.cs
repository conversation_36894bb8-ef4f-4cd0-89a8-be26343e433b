using UnityEngine;

/// <summary>
/// 存储噪声地图生成所需参数的ScriptableObject。
/// </summary>
[System.Serializable]
public class Biome
{
    public string name;
    [Range(0, 1)]
    public float height;
    public Color color;
    [Range(0.001f, 1f)]
    public float blendStrength = 0.1f;
}

[CreateAssetMenu(fileName = "NewNoiseData", menuName = "The Lightless Crown/Map/Noise Data")]
public class NoiseDataSO : ScriptableObject
{
    [Header("生物群系设置")]
    public Biome[] biomes;

    [Header("地图基础设置")]
    public int mapWidth = 256;
    public int mapHeight = 256;
    public int seed = 12345;

    [Header("噪声参数")]
    public float noiseScale = 50f;
    public int octaves = 4;
    [Range(0, 1)]
    public float persistence = 0.5f;
    public float lacunarity = 2f;
    public Vector2 offset = Vector2.zero;

    [Header("多倍频程设置")]
    public bool useCustomAmplitudes = false;
    public float[] customAmplitudes = { 1f, 0.5f, 0.25f, 0.125f, 0.0625f, 0.03125f };
    public bool normalizeAmplitudes = true;

    [Header("高度重分布")]
    public bool useRedistribution = true;
    public RedistributionType redistributionType = RedistributionType.Power;
    public float redistributionExponent = 1.2f;
    public AnimationCurve redistributionCurve = AnimationCurve.Linear(0, 0, 1, 1);
    public float terraceCount = 5f;
    public float terraceSmoothing = 0.1f;

    [Header("圆滑化设置")]
    public bool useSmoothTerrain = true;
    public int smoothingIterations = 2;
    [Range(0, 1)]
    public float smoothingStrength = 0.5f;

    /// <summary>
    /// 在编辑器中验证参数
    /// </summary>
    private void OnValidate()
    {
        if (mapWidth < 1) mapWidth = 1;
        if (mapHeight < 1) mapHeight = 1;
        if (lacunarity < 1) lacunarity = 1;
        if (octaves < 0) octaves = 0;
        if (noiseScale <= 0) noiseScale = 0.0001f;
    }

    /// <summary>
    /// 根据高度值获取生物群系颜色
    /// </summary>
    public Color GetBiomeColor(float height)
    {
        if (biomes == null || biomes.Length == 0)
        {
            return Color.magenta; // 如果没有定义生物群系，返回一个显眼的颜色
        }

        // 遍历所有生物群系，找到第一个高度阈值大于等于当前高度的
        foreach (var biome in biomes)
        {
            if (height <= biome.height)
            {
                return biome.color;
            }
        }

        // 如果高度超过了所有定义的阈值，返回最后一个生物群系的颜色
        return biomes[biomes.Length - 1].color;
    }
}