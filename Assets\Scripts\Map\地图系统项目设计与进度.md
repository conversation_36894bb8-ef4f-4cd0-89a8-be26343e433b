地图系统项目设计与进度.md 请阅读这个文档。

项目名称：2D游戏地图系统
文档目标：记录项目进度，记录项目设计思路，开发时根据这个文档进行开发。
设计要求：一个独立的地图系统模块。尽可能的减少对外部其他模块的依赖耦合。
预期效果：地图系统模块可以独立运行，可以单独测试。可以像导航软件一样放大缩小，点击目的地可以自动寻路。
关于噪声地图生成参考：https://www.redblobgames.com/maps/terrain-from-noise/
设计思路：
    1. **核心架构设计 (MVS)**
        -   **数据层 (Model):** 负责原始地图数据的管理。定义地图数据结构，如Grid（格子）、Chunk（区块）、POI（兴趣点）等。实现数据的加载、存储和查询接口。数据可以考虑使用 `ScriptableObject` 存储固定地图信息，使用 `JSON` 或二进制格式存储玩家动态生成或改变的数据。
        -   **表现层 (View):** 负责将地图数据可视化地呈现给玩家。包括地形的渲染、地标/图标的绘制、角色位置的标记、寻路路径的显示等。这一层应该只关心“如何显示”，而不关心数据来源和业务逻辑。
            -   **多细节层次渲染 (LOD Rendering):** 对于河流、山脉、国界等大型地理特征，表现层需要根据当前缩放级别，选择合适的视觉表现。例如，在世界层级，一条河可能被渲染成简单的蓝色线条；在本地层级，则被渲染成有宽度、有水流动画的精细模型。这要求表现层能够处理来自数据层的多套表现形式（LOD数据）。
        -   **控制层 (Service/Controller):** 系统的中枢，处理用户输入（如拖动、缩放、点击）和复杂的业务逻辑。例如，接收用户的点击事件，调用寻路模块计算路径，然后将路径数据交给表现层进行绘制。
    2. **地图细节层级 (LOD) 与无缝缩放**
        -   **核心理念:** 摒弃多场景加载切换的模式。整个地图是一个连续的、可无缝缩放的整体。不同“层级”的地图（如世界、城市、村镇）代表的是在不同缩放级别下，所显示内容的详细程度。
        -   **核心澄清：单一数据源，多种视图**
            -   **我们始终在操作同一张地图的数据。** 不论是世界、区域还是本地，它们都是对同一份完整的地图数据应用了不同“滤镜”的结果。
            -   **数据是统一的：** 存在一份包含所有信息的“原始地图数据”，它定义了从大陆板块到单个NPC的所有内容及其坐标。
            -   **视图是动态的：** 三个缩放级别（世界、区域、本地）仅仅是根据缩放程度，决定从这份原始数据中“提取”哪些信息并显示出来的“视图规则”。这保证了数据的一致性和交互的无缝性。
        -   **细节分级 (LOD):** 为地图上的每一种POI（兴趣点，如省份名、城市名、地牢入口、NPC）定义一个“重要性”或“显示优先级”等级。
        -   **动态显隐:** `MapController` 会根据当前的缩放级别（Zoom Level），动态决定哪些等级的POI应该被显示。
        -   **层级细化定义:**
            -   **世界层 (World Level - 缩放比例 0% ~ 20%):**
                -   **显示:**
                    -   大陆/国家/省份的名称和边界。
                    -   最重要的大都市或阵营主城图标及名称。
                    -   连接主城的主干道。
                -   **隐藏:**
                    -   普通城镇、村庄、野外地点。
                    -   次要道路。
                    -   所有功能性NPC和怪物。
            -   **区域层 (Region Level - 缩放比例 20% ~ 60%):**
                -   **显示:**
                    -   该区域内的所有城镇、村庄、大型副本入口、特殊野外地点的图标和名称。
                    -   连接这些地点的主要和次要道路。
                    -   重要的功能性NPC图标（如铁匠、商人），但不显示名称。
                -   **隐藏:**
                    -   普通怪物、采集点。
                    -   次要NPC（如普通村民）。
                    -   区域内的地形细节装饰物。
            -   **本地层 (Local Level - 缩放比例 60% ~ 100%):**
                -   **显示:**
                    -   所有POI的图标和名称，包括任务NPC、怪物、采集点等。
                    -   玩家及队友的精确位置标记。
                    -   完整的地形和道路细节。
        -   **性能优化:** 这个机制是性能优化的关键。通过在地图缩小时剔除大量不必要的UI元素或模型，可以极大地降低渲染开销。
    3. **坐标系统**
        -   建立一套统一的坐标映射体系。核心是 **世界坐标 (World Position)**。
        -   需要提供接口，能够方便地进行以下转换：
            -   世界坐标 <-> 不同层级地图的本地坐标
            -   世界坐标 <-> UI屏幕坐标 (用于点击检测和UI元素定位)
            -   地图本地坐标 <-> 格子/区块索引
    4. **地图数据结构与存储**
        -   **POIData (Point of Interest):** 定义地图上的“点状”兴趣点。
            -   **核心字段:** ID, 名称, 坐标, **显示优先级/LOD等级 (用于分层显隐)**, 图标等。
        -   **GeoFeatureData (Geographical Feature):** 定义地图上的“线状”或“面状”地理特征，如河流、山脉、国界等。
            -   **核心理念:** 为同一地理特征提供多套表现形式，以支持LOD渲染。
            -   **示例 (河流):**
                -   `detailedPath`: 用于本地层的高精度路径点数组。
                -   `simplifiedPath`: 用于世界层的低精度路径点数组。
                -   `localMaterial/Width`: 本地层使用的材质和宽度。
                -   `worldMaterial/Width`: 世界层使用的材质和宽度。
        -   **MapData (ScriptableObject):** 作为地图数据的容器，包含一个 `POIData` 列表和一个 `GeoFeatureData` 列表。
        -   存储方案：固定不变的数据（如世界地图布局、城市位置、河流山脉走向）使用 `ScriptableObject`，方便在Editor中配置。程序化生成或动态变化的数据（如随机地牢的布局）可以在运行时生成，或保存为JSON/二进制文件。
    5. **自动寻路系统 (Pathfinding)**
        -   **算法:** 推荐使用 **A*** 算法，在效率和效果上表现均衡。
        -   **寻路数据:**
            -   对于2D格子地图，可以直接在格子上进行A*寻路。
            -   对于更复杂的3D或2D地形，可以考虑使用 Unity 自带的 **NavMesh** 系统，或者自定义的 **Waypoint（路点）** 网络。
        -   **连续地图寻路:** 由于是单一连续地图，不再有“跨层级寻路”的复杂逻辑。寻路系统只需在统一的寻路数据（例如，一张覆盖整个游戏世界的大型NavMesh或路点网络）上计算出从起点到终点的完整路径即可。
    6. **地图生成 (2D)**
        -   **固定区域:** 主要通过下面将要提到的 Editor 编辑器来手动设计和放置。
        -   **可变/随机区域 (基于柏林噪声):** 采用多层噪声叠加的方法生成丰富的2D地形。流程参考自 [Red Blob Games] (https://www.redblobgames.com/maps/terrain-from-noise/)。
            -   **Step 1: 生成基础海拔图 (Elevation Map)**
                -   使用 `Mathf.PerlinNoise(x * scale, y * scale)` 函数，遍历地图的每一个格子 (x, y) 生成一个噪声值（通常在 0-1 之间）。
                -   `scale` 参数控制噪声的“缩放”，决定了地形特征（如山脉、大陆）的大小。可以叠加多个不同 scale 和权重的噪声（即多倍频程噪声，Octaves）来增加细节。
                -   将生成的噪声值直接映射为海拔高度。
            -   **Step 2: 海拔重分布 (Redistribution)**
                -   直接使用柏lin噪声得到的地形通常中间高、四周低，缺乏明确的海岸线和岛屿感。
                -   需要一个函数来“重塑”噪声值。例如，可以设计一个函数，将靠近中心区域的噪声值进行提升，形成中心大陆；或者应用一个 `power` 函数 (`Mathf.Pow(noiseValue, power)`) 来锐化地形，使得平原更平，山地更陡。
            -   **Step 3: 生成湿度图 (Moisture Map)**
                -   使用**另一组不同种子或偏移量**的柏林噪声，生成第二张独立的噪声图，作为基础湿度图。
                -   这代表了地区的降雨量分布。同样可以叠加多倍频程噪声来模拟更自然的湿度变化。
            -   **Step 4: 确定生物群系 (Biome)**
                -   根据每个格子的**海拔值**和**湿度值**，共同决定该格子的最终地形/生物群系。
                -   创建一个二维查找表或决策逻辑：
                    -   低海拔 + 任意湿度 -> **海洋/水域**
                    -   中海拔 + 低湿度 -> **沙漠/干草原**
                    -   中海拔 + 中湿度 -> **平原/草地**
                    -   中海拔 + 高湿度 -> **森林/沼泽**
                    -   高海拔 + 低湿度 -> **岩石山地**
                    -   高海拔 + 高湿度 -> **雪山**
                -   将这些生物群系与具体的 Tile (瓦片) 或颜色关联起来，即可完成地图的可视化渲染。
            -   **种子 (Seed):** 整个生成过程的起点是一个随机种子。只要种子不变，生成的噪声图就是完全一致的，这保证了地图的可复现性。
    7. **Editor 编辑器**
        -   使用 `EditorWindow` 创建一个自定义的“地图编辑器”窗口。
        -   **功能:**
            -   可视化地创建和编辑 `MapData`、`POIData` 和 `GeoFeatureData`。
            -   在场景视图或编辑器窗口中直接点击放置POI、绘制障碍区、设置传送点。
            -   为POI和地理特征方便地指定其LOD层级和多种表现形式。
            -   提供“一键烘焙”功能，将编辑好的数据生成为最终的寻路网格或数据文件。
            -   支持导入/导出地图配置，方便备份和协作。
    8. **交互与功能**
        -   实现地图的拖拽、以鼠标或触摸中心为锚点的缩放功能。
        -   实现点击地图上的POI（Point of Interest，兴趣点）显示详细信息的功能（通过事件广播）。
        -   实现角色在小地图/大地图上的位置和朝向实时更新。
        -   点击地图任意位置，角色可以自动寻路到该点。
    9. **补充设计考量**
        -   **性能与优化 (Performance & Optimization):**
            -   **对象池 (Object Pooling):** 为地图上需要频繁显隐的POI图标（特别是本地层的NPC、怪物）建立对象池，避免运行时频繁创建和销毁对象带来的GC开销。
            -   **渲染合批 (Batching):** 对于2D地图，应使用 `Sprite Atlas` 将UI图标和小物件的贴图打包，并尽可能复用材质，以减少Draw Call，提升渲染效率。
            -   **数据/资源加载策略:** 考虑对地图数据进行分块（Chunk-based），实现动态加载和卸载，只在内存中保留玩家当前位置附近区域的详细数据。
        -   **交互与用户体验 (Interaction & UX):**
            -   **平滑过渡 (Tweening/Lerping):** POI的显隐可以加入淡入淡出效果；镜头移动应使用平滑插值，而不是瞬间跳转，提升视觉体验。
            -   **信息提示 (Tooltips):** 实现鼠标悬停在POI上时，显示一个包含其名称和简要信息的浮窗。
        -   **模块解耦与扩展性 (Decoupling & Extensibility):**
            -   **事件驱动 (Event-Driven):** 地图系统与外部系统（任务、UI、战斗）的交互严格通过全局事件中心进行。例如，地图系统只广播“OnPOIClicked(POIData poi)”事件，而不直接调用任何其他模块的代码。
            -   **服务接口 (Service Interface):** 定义一个清晰的 `IMapService` 接口，暴露地图系统的核心功能（如 `RequestPath(Vector2 destination)`、`GetCurrentPlayerCoordinate()`），供其他模块调用，实现高内聚、低耦合。

任务规划：
   1. **随机地图基础地形生成验证**
      - 参考网址`https://www.redblobgames.com/maps/terrain-from-noise/` 的技术方案来实现。
      - 需要实现的功能：
        - 生成基础海拔图
        - 脚本变量可手动变更，观察地图生成效果。
        - 需要输入种子来固定噪声图，图形偏移也能保持完整性。
        - 地形要曲线圆滑，在参数文件中加入两个地形边缘的平滑度设置。
        - 在SO参数文件中，加入对地形颜色的配置，实现地形颜色配置。
   2. **地图数据结构与存储**