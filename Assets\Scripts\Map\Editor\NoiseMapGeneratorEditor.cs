using UnityEngine;
using UnityEditor;

/// <summary>
/// NoiseMapGenerator的自定义编辑器
/// </summary>
[CustomEditor(typeof(NoiseMapGenerator))]
public class NoiseMapGeneratorEditor : Editor
{
    private NoiseMapGenerator generator;
    private Editor dataEditor;

    void OnEnable()
    {
        generator = target as NoiseMapGenerator;
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        // 检查修改
        EditorGUI.BeginChangeCheck();

        // 绘制NoiseDataSO字段
        SerializedProperty noiseDataProp = serializedObject.FindProperty("noiseData");
        EditorGUILayout.PropertyField(noiseDataProp, new GUIContent("噪声数据"));

        if (noiseDataProp.objectReferenceValue != null)
        {
            SerializedObject noiseDataObj = new SerializedObject(noiseDataProp.objectReferenceValue);
            noiseDataObj.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("噪声数据参数", EditorStyles.boldLabel);

            // 迭代并绘制所有属性
            SerializedProperty prop = noiseDataObj.GetIterator();
            if (prop.NextVisible(true))
            {
                do
                {
                    if (prop.name == "m_Script") continue;

                    // 对seed字段进行特殊处理，添加随机按钮
                    if (prop.name == "seed")
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.PropertyField(prop, new GUIContent("种子"), true);
                        if (GUILayout.Button("随机", GUILayout.Width(50)))
                        {
                            prop.intValue = Random.Range(0, 100000);
                            noiseDataObj.ApplyModifiedProperties();
                            generator.GenerateMap();
                            GUI.changed = false; // 阻止EndChangeCheck再次触发
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                    else if (prop.name == "redistributionType")
                    {
                        prop.enumValueIndex = EditorGUILayout.Popup(new GUIContent("重分布类型"), prop.enumValueIndex, new[] { "无", "幂函数", "自定义曲线", "梯田", "反向幂函数" });
                    }
                    else
                    {
                        EditorGUILayout.PropertyField(prop, new GUIContent(GetChineseDisplayName(prop.name)), true);
                    }
                }
                while (prop.NextVisible(false));
            }
            if (GUI.changed)
            {
                noiseDataObj.ApplyModifiedProperties();
            }
        }
        else
        {
            EditorGUILayout.HelpBox("请分配一个噪声数据 (NoiseDataSO) 文件。", MessageType.Warning);
        }

        EditorGUILayout.Space();

        // 预览设置
        DrawPreviewSettings();

        EditorGUILayout.Space();

        // 操作按钮
        DrawActionButtons();

        // 应用修改并触发更新
        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
            if (generator.autoUpdate)
            {
                generator.GenerateMap();
            }
        }

        // 绘制曲线图预览
        if (generator.curveTexture != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("地形剖面曲线预览", EditorStyles.boldLabel);
            GUILayout.Box(generator.curveTexture, GUILayout.Height(128), GUILayout.ExpandWidth(true));
        }
    }

    private void DrawPreviewSettings()
    {
        EditorGUILayout.LabelField("预览和导出设置", EditorStyles.boldLabel);

        EditorGUILayout.PropertyField(serializedObject.FindProperty("autoUpdate"), new GUIContent("自动更新"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("textureRenderer"), new GUIContent("纹理渲染器"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("textureFilterMode"), new GUIContent("纹理过滤模式"));

        // 注意：曲线图不直接使用地形着色，但我们保留该选项以备将来扩展
        SerializedProperty useTerrainColoring = serializedObject.FindProperty("useTerrainColoring");
        EditorGUILayout.PropertyField(useTerrainColoring, new GUIContent("使用地形着色"));

        if (useTerrainColoring.boolValue)
        {
            EditorGUI.indentLevel++;
            var colorModeProp = serializedObject.FindProperty("colorMode");
            colorModeProp.enumValueIndex = EditorGUILayout.Popup(new GUIContent("着色模式"), colorModeProp.enumValueIndex, new[] { "灰度图", "生物群系", "高度着色" });
            EditorGUI.indentLevel--;
        }
    }

    private void DrawActionButtons()
    {
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("生成地图", GUILayout.Height(30)))
        {
            generator.GenerateMap();
            // 阻止由于curveTexture更新导致的OnInspectorGUI重绘触发的二次生成
            GUI.changed = false;
        }


        EditorGUILayout.EndHorizontal();

        if (generator.GetNoiseMap() != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox($"当前地图尺寸: {generator.GetNoiseMap().GetLength(0)} x {generator.GetNoiseMap().GetLength(1)}", MessageType.Info);
        }
    }

    private string GetChineseDisplayName(string propertyName)
    {
        switch (propertyName)
        {
            case "biomes": return "生物群系";
            case "mapWidth": return "地图宽度";
            case "mapHeight": return "地图高度";
            case "noiseScale": return "噪声缩放";
            case "octaves": return "倍频程";
            case "persistence": return "持续度";
            case "lacunarity": return "空隙度";
            case "offset": return "偏移";
            case "useCustomAmplitudes": return "使用自定义振幅";
            case "customAmplitudes": return "自定义振幅";
            case "normalizeAmplitudes": return "标准化振幅";
            case "useRedistribution": return "使用高度重分布";
            case "redistributionType": return "重分布类型";
            case "redistributionExponent": return "重分布指数";
            case "redistributionCurve": return "重分布曲线";
            case "terraceCount": return "梯田数量";
            case "terraceSmoothing": return "梯田平滑度";
            case "useSmoothTerrain": return "使用平滑地形";
            case "smoothingIterations": return "平滑迭代次数";
            case "smoothingStrength": return "平滑强度";
            default: return ObjectNames.NicifyVariableName(propertyName);
        }
    }
}
